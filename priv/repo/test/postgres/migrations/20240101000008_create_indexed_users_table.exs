defmodule Drops.Repos.Postgres.Migrations.CreateIndexedUsersTable do
  use Ecto.Migration

  def change do
    create table(:indexed_users) do
      add :email, :string
      add :username, :string
      add :status, :string

      timestamps()
    end

    # Create indices for testing index-based finders
    create unique_index(:indexed_users, [:email])
    create index(:indexed_users, [:username])
    create index(:indexed_users, [:status])
  end
end
