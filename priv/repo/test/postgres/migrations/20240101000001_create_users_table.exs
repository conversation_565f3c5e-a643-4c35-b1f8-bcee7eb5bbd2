defmodule Drops.Repos.Postgres.Migrations.CreateUsersTable do
  use Ecto.Migration

  def change do
    create table(:users) do
      add :name, :string
      add :email, :string
      add :age, :integer

      timestamps()
    end

    # Create indices for testing index-based finders and introspector
    create unique_index(:users, [:email])
    create index(:users, [:name])
    create index(:users, [:name, :age])

    # Create a simple table for testing minimal indices
    create table(:simple_data) do
      add :data, :string
    end

    # Create user_groups join table for associations testing
    create table(:user_groups) do
      add :user_id, references(:users, on_delete: :delete_all)
      add :group_id, :integer
    end

    create unique_index(:user_groups, [:user_id, :group_id])
  end
end
