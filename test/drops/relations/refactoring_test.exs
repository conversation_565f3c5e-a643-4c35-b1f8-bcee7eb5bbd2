defmodule Drops.Relations.RefactoringTest do
  use Drops.OperationCase, async: false

  describe "refactored query API" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "module-level functions are callable with proper options" do
      # Test that the module-level functions can be called directly with proper options
      # This verifies that the refactoring successfully moved functions to module level

      defmodule Test.Relations.ModuleLevelUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # Clean up any existing data
      Drops.TestRepo.delete_all(Test.Relations.ModuleLevelUsers.Struct)

      # Test that module-level functions work when called with proper options
      struct_module = Test.Relations.ModuleLevelUsers.Struct

      # Test all function - should return empty list
      assert Drops.Relation.all(struct_module,
               repo: Drops.TestRepo,
               relation: Test.Relations.ModuleLevelUsers
             ) == []

      # Test count function - should return 0
      assert Drops.Relation.count(struct_module, :count,
               repo: Drops.TestRepo,
               relation: Test.Relations.ModuleLevelUsers
             ) == 0

      # Test get_by function - should return nil for non-existent record
      assert Drops.Relation.get_by(struct_module, [name: "nonexistent"],
               repo: Drops.TestRepo,
               relation: Test.Relations.ModuleLevelUsers
             ) == nil

      # Test insert function with plain map
      {:ok, user} =
        Drops.Relation.insert(%{name: "Test User", email: "<EMAIL>"},
          repo: Drops.TestRepo,
          relation: Test.Relations.ModuleLevelUsers
        )

      assert user.name == "Test User"
      assert user.email == "<EMAIL>"

      # Test get function
      found_user =
        Drops.Relation.get(struct_module, user.id,
          repo: Drops.TestRepo,
          relation: Test.Relations.ModuleLevelUsers
        )

      assert found_user.id == user.id
      assert found_user.name == "Test User"
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "delegating functions work correctly" do
      defmodule Test.Relations.RefactoringUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # Test that all expected functions are exported
      functions_to_test = [
        {:get, 2},
        {:get!, 2},
        {:get_by, 2},
        {:get_by!, 2},
        {:all, 1},
        {:one, 1},
        {:one!, 1},
        {:insert, 1},
        {:insert!, 1},
        {:update, 2},
        {:update!, 2},
        {:delete, 1},
        {:delete!, 1},
        {:count, 1},
        {:first, 1},
        {:last, 1}
      ]

      for {func, arity} <- functions_to_test do
        assert function_exported?(Test.Relations.RefactoringUsers, func, arity),
               "Function #{func}/#{arity} should be exported"
      end

      # Test that the functions delegate correctly by checking they can be called
      # (even if they fail due to missing data, they should reach the repo layer)

      # Clean up any existing data
      Drops.TestRepo.delete_all(Test.Relations.RefactoringUsers.Struct)

      # Test count function
      assert Test.Relations.RefactoringUsers.count() == 0

      # Test all function
      assert Test.Relations.RefactoringUsers.all() == []

      # Test that repo option can be overridden (this should work even with a different repo)
      # We'll test this by ensuring the function accepts the option without error
      assert Test.Relations.RefactoringUsers.count(nil, repo: Drops.TestRepo) == 0
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    test "repo option is automatically passed from use macro" do
      defmodule Test.Relations.RepoOptionUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # The functions should work without explicitly passing repo
      # because it's automatically set from the use macro
      assert Test.Relations.RepoOptionUsers.count() == 0
      assert Test.Relations.RepoOptionUsers.all() == []
    end
  end
end
