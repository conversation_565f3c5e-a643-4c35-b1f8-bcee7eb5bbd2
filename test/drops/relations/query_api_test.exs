defmodule Drops.Relations.QueryAPISpec do
  use Drops.RelationCase, async: false

  describe "query API functions" do
    @tag relations: [:query_users_data]
    test "query functions work with actual data", %{query_users_data: relation} do
      # Clean up any existing data
      relation.delete_all()

      # Test count on empty table
      assert relation.count() == 0

      # Test all on empty table
      assert relation.all() == []

      # Insert a user using Ecto changeset to handle timestamps properly
      user_struct = relation.struct(%{name: "Test User", email: "<EMAIL>"})
      changeset = Ecto.Changeset.cast(user_struct, %{}, [:name, :email])
      {:ok, _} = Drops.TestRepo.insert(changeset)

      # Get the inserted user to test with
      [user] = relation.all()
      assert user.name == "Test User"

      # Test count after insert
      assert relation.count() == 1

      # Test get
      found_user = relation.get(user.id)
      assert found_user.name == "Test User"
      assert found_user.email == "<EMAIL>"

      # Test get_by
      found_by_email = relation.get_by(email: "<EMAIL>")
      assert found_by_email.id == user.id

      # Test all
      all_users = relation.all()
      assert length(all_users) == 1
      assert hd(all_users).id == user.id

      # Test update (using changeset)
      changeset = Ecto.Changeset.change(user, %{name: "Updated User"})
      {:ok, updated_user} = relation.update(changeset)
      assert updated_user.name == "Updated User"

      # Test delete
      {:ok, _deleted_user} = relation.delete(updated_user)
      assert relation.count() == 0
    end
  end

  describe "index-based finders" do
    @tag relations: [:users]
    test "generates get_by_{field} functions for indexed fields" do
      # Create a table with indices for testing
      Ecto.Adapters.SQL.query!(
        Drops.TestRepo,
        """
        CREATE TABLE IF NOT EXISTS test_indexed_users_query (
          id INTEGER PRIMARY KEY,
          email TEXT UNIQUE,
          username TEXT,
          status TEXT
        )
        """,
        []
      )

      # Create indices
      Ecto.Adapters.SQL.query!(
        Drops.TestRepo,
        "CREATE INDEX IF NOT EXISTS idx_test_indexed_users_query_username ON test_indexed_users_query(username)",
        []
      )

      Ecto.Adapters.SQL.query!(
        Drops.TestRepo,
        "CREATE INDEX IF NOT EXISTS idx_test_indexed_users_query_status ON test_indexed_users_query(status)",
        []
      )

      defmodule Test.Relations.IndexedUsersQuery do
        use Drops.Relation,
          repo: Drops.TestRepo,
          name: "test_indexed_users_query",
          infer: true
      end

      # Test that index-based finder functions exist
      index_functions = [:get_by_email, :get_by_username, :get_by_status]

      for func <- index_functions do
        assert function_exported?(Test.Relations.IndexedUsersQuery, func, 1),
               "Function #{func}/1 should be exported"
      end

      # Test actual usage
      # Insert test data
      Ecto.Adapters.SQL.query!(
        Drops.TestRepo,
        "INSERT OR REPLACE INTO test_indexed_users_query (email, username, status) VALUES (?, ?, ?)",
        ["<EMAIL>", "testuser", "active"]
      )

      # Test the finders
      user_by_email = Test.Relations.IndexedUsersQuery.get_by_email("<EMAIL>")
      assert user_by_email != nil
      assert user_by_email.username == "testuser"

      user_by_username = Test.Relations.IndexedUsersQuery.get_by_username("testuser")
      assert user_by_username != nil
      assert user_by_username.email == "<EMAIL>"

      user_by_status = Test.Relations.IndexedUsersQuery.get_by_status("active")
      assert user_by_status != nil
      assert user_by_status.username == "testuser"
    end
  end

  describe "nested Schema module" do
    @tag relations: [:users]
    test "generates proper Ecto.Schema module" do
      defmodule Test.Relations.SchemaUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      # Test that the Struct module exists and behaves like an Ecto.Schema
      assert Code.ensure_loaded?(Test.Relations.SchemaUsers.Struct)

      # Test Ecto.Schema functions
      assert Test.Relations.SchemaUsers.ecto_schema(:source) == "users"
      assert :id in Test.Relations.SchemaUsers.ecto_schema(:fields)
      assert :name in Test.Relations.SchemaUsers.ecto_schema(:fields)
      assert :email in Test.Relations.SchemaUsers.ecto_schema(:fields)

      # Test that we can create structs (using apply to avoid compile-time issues)
      struct_module = Test.Relations.SchemaUsers.Struct
      user_struct = struct(struct_module, %{name: "Test", email: "<EMAIL>"})
      assert user_struct.name == "Test"
      assert user_struct.email == "<EMAIL>"

      # Test that the struct works with Ecto.Repo functions
      {:ok, inserted_user} = Drops.TestRepo.insert(user_struct)
      assert inserted_user.name == "Test"
      assert inserted_user.email == "<EMAIL>"
    end
  end

  describe "parent module schema() function" do
    @tag relations: [:users]
    test "provides access to Drops.Relation.Schema" do
      defmodule Test.Relations.SchemaAccessUsers do
        use Drops.Relation, repo: Drops.TestRepo, name: "users", infer: true
      end

      schema = Test.Relations.SchemaAccessUsers.schema()
      assert schema.__struct__ == Drops.Relation.Schema
      assert schema.source == "users"
      assert length(schema.fields) > 0

      # Check that fields contain expected field structs
      field_names = Enum.map(schema.fields, & &1.name)
      assert :id in field_names
      assert :name in field_names
      assert :email in field_names
    end
  end
end
